import uuid
import asyncio
import os
from datetime import datetime
from typing import Any, Dict, List
from pymongo import MongoClient

from langchain_core.messages import AIMessage, SystemMessage

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states.onboarding_state import (
    OnboardingState,
    PlanDetails,
    SidebarStateData,
)
from athlea_langgraph.utils.prompt_loader import get_prompt_loader
import logging

logger = logging.getLogger(__name__)


# Simplified JSON schema to match frontend's PlanDetails
PLAN_DETAILS_SCHEMA = {
    "title": "PlanDetails",
    "description": "Schema for a personalized fitness plan structure.",
    "type": "object",
    "properties": {
        "planId": {"type": "string", "description": "Unique identifier for the plan"},
        "name": {
            "type": "string",
            "description": "Catchy and descriptive name for the fitness plan",
        },
        "description": {
            "type": "string",
            "description": "Detailed description of the plan and its goals",
        },
        "duration": {"type": "string", "description": "Total duration of the plan"},
        "level": {"type": "string", "description": "Recommended fitness level"},
        "planType": {
            "type": "string",
            "enum": ["Running", "Cycling", "Strength", "Nutrition", "Recovery"],
        },
        "disciplines": {"type": "array", "items": {"type": "string"}},
        "rationale": {
            "type": "string",
            "description": "Explanation of why this plan is suitable for the user",
        },
        "phases": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "phaseName": {"type": "string"},
                    "duration": {"type": "string"},
                    "weeks": {"type": "string"},
                    "focus": {"type": "string"},
                    "description": {"type": "string"},
                    "rationale": {"type": "string"},
                },
                "required": [
                    "phaseName",
                    "duration",
                    "weeks",
                    "focus",
                    "description",
                    "rationale",
                ],
            },
        },
        "exampleSessions": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "SessionName": {"type": "string"},
                    "sessionType": {"type": "string"},
                    "Duration": {"type": "string"},
                    "SessionDescription": {"type": "string"},
                },
                "required": [
                    "SessionName",
                    "sessionType",
                    "Duration",
                    "SessionDescription",
                ],
            },
        },
    },
    "required": [
        "planId",
        "name",
        "description",
        "duration",
        "level",
        "planType",
        "disciplines",
        "rationale",
        "phases",
    ],
}


class GeneratePlanNode:
    """Node for generating a personalized fitness plan"""

    def __init__(self):
        self.llm = create_azure_chat_openai(temperature=0.3)
        self.planning_prompt_template = None  # Will be loaded lazily

        # MongoDB persistence setup
        self.mongodb_uri = os.getenv("MONGODB_URI")
        self.dev_mode = os.getenv("ONBOARDING_DEV_MODE", "false").lower() == "true"
        self._mongo_client = None

        # LLM timeout configuration
        self.llm_timeout_seconds = 60  # 60 second timeout for LLM calls
        self.max_retries = 2  # Maximum number of retries for LLM calls

        # Fallback prompt template
        self.fallback_prompt_template = """You are an expert fitness coach. Based on the user's goals and summarized information, generate a high-level training plan.

User Goals: {user_goals}
User Information Summary:
 - {summary_string}

CRITICAL: Generate a plan structure that follows the JSON schema precisely.
- Create a plan with a name, description, duration, level, and rationale.
- Include 1-3 high-level phases with names, durations, and descriptions.
- Include 2-3 example sessions with names, types, and descriptions.
- DO NOT generate detailed daily schedules or week-by-week breakdowns."""

    async def _get_planning_prompt_template(self) -> str:
        """Load the planning prompt template lazily"""
        if self.planning_prompt_template is None:
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt(
                    "onboarding/fitness_plan_generation"
                )
                self.planning_prompt_template = prompt_config.get_rendered_prompt()
                logger.info(
                    "Successfully loaded onboarding fitness plan generation template prompt from file"
                )
            except Exception as e:
                logger.error(
                    f"Failed to load onboarding fitness plan generation template prompt: {e}"
                )
                self.planning_prompt_template = self.fallback_prompt_template

        return self.planning_prompt_template

    def _validate_and_fix_plan_data(self, plan_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and fix plan data to ensure it meets schema requirements"""
        try:
            logger.info("[Node: generatePlanNode] Validating and fixing plan data...")

            # Make a copy to avoid modifying the original
            fixed_data = plan_data.copy() if isinstance(plan_data, dict) else {}

            # Fix disciplines - ensure they're valid values
            if "disciplines" in fixed_data and isinstance(
                fixed_data["disciplines"], list
            ):
                fixed_disciplines = []
                for discipline in fixed_data["disciplines"]:
                    # Map common values to valid ones
                    discipline_mapping = {
                        "strength training": "Strength",
                        "weight training": "Strength",
                        "resistance training": "Strength",
                        "cardio": "Running",
                        "endurance": "Running",
                        "bike": "Cycling",
                        "bicycle": "Cycling",
                        "diet": "Nutrition",
                        "eating": "Nutrition",
                        "rest": "Recovery",
                        "sleep": "Recovery",
                    }

                    mapped_discipline = discipline_mapping.get(
                        discipline.lower(), discipline
                    )

                    # Only allow valid disciplines
                    if mapped_discipline in [
                        "Running",
                        "Cycling",
                        "Strength",
                        "Nutrition",
                        "Recovery",
                    ]:
                        fixed_disciplines.append(mapped_discipline)
                    else:
                        # Default to Strength if we can't map it
                        fixed_disciplines.append("Strength")
                fixed_data["disciplines"] = list(
                    set(fixed_disciplines)
                )  # Remove duplicates

            # Fix phases - ensure disciplineSpecifics is present
            if "phases" in fixed_data and isinstance(fixed_data["phases"], list):
                for phase in fixed_data["phases"]:
                    if "disciplineSpecifics" not in phase:
                        phase["disciplineSpecifics"] = {}

                    # Ensure disciplineSpecifics has the right structure
                    discipline_specifics = phase["disciplineSpecifics"]
                    if not isinstance(discipline_specifics, dict):
                        phase["disciplineSpecifics"] = {}

            # Ensure required fields exist
            if "planId" not in fixed_data or not fixed_data["planId"]:
                fixed_data["planId"] = str(uuid.uuid4())

            if "exampleSessions" not in fixed_data:
                fixed_data["exampleSessions"] = []

            logger.info("[Node: generatePlanNode] Plan data validation completed")
            return fixed_data

        except Exception as e:
            logger.error(f"[Node: generatePlanNode] Error validating plan data: {e}")
            # Return the original data if validation fails
            return plan_data if isinstance(plan_data, dict) else {}

    def _create_fallback_plan(
        self, sidebar_data: SidebarStateData, failed_plan_data: Dict[str, Any] = None
    ) -> PlanDetails:
        """Create a simplified fallback plan when validation fails"""
        logger.info("[Node: generatePlanNode] Creating fallback plan...")

        try:
            # Determine the primary sport/discipline
            primary_discipline = "Strength"  # Default
            if sidebar_data.selected_sports:
                # Map common sport names to valid disciplines
                sport_mapping = {
                    "strength training": "Strength",
                    "strength": "Strength",
                    "running": "Running",
                    "cycling": "Cycling",
                    "nutrition": "Nutrition",
                    "recovery": "Recovery",
                }
                first_sport = sidebar_data.selected_sports[0].lower()
                primary_discipline = sport_mapping.get(first_sport, "Strength")

            # Extract goals for plan name
            goals_text = "Fitness"
            if sidebar_data.goals and sidebar_data.goals.list:
                goals_text = " and ".join(
                    sidebar_data.goals.list[:2]
                )  # Take first 2 goals

            # Create a minimal valid plan structure
            fallback_plan_data = {
                "planId": (failed_plan_data or {}).get("planId", str(uuid.uuid4())),
                "name": f"Personalized {primary_discipline} Plan",
                "description": f"A comprehensive {primary_discipline.lower()} training plan focused on {goals_text}.",
                "duration": "12 weeks",
                "level": "Intermediate",
                "planType": primary_discipline,
                "disciplines": [primary_discipline],
                "rationale": f"This plan is designed to help you achieve your goals of {goals_text} through structured {primary_discipline.lower()} training.",
                "phases": [
                    {
                        "phaseName": "Foundation Phase",
                        "duration": "4 weeks",
                        "weeks": "1-4",
                        "focus": "Building fundamentals",
                        "description": f"Focus on establishing proper form and building a foundation in {primary_discipline.lower()}.",
                        "rationale": "A strong foundation is essential for long-term progress and injury prevention.",
                        "disciplineSpecifics": {},  # Empty dict to satisfy validation
                    },
                    {
                        "phaseName": "Development Phase",
                        "duration": "4 weeks",
                        "weeks": "5-8",
                        "focus": "Progressive development",
                        "description": f"Progressively increase intensity and complexity in your {primary_discipline.lower()} training.",
                        "rationale": "Gradual progression allows for sustainable improvements while minimizing injury risk.",
                        "disciplineSpecifics": {},
                    },
                    {
                        "phaseName": "Optimization Phase",
                        "duration": "4 weeks",
                        "weeks": "9-12",
                        "focus": "Performance optimization",
                        "description": f"Fine-tune your {primary_discipline.lower()} performance and achieve your specific goals.",
                        "rationale": "The final phase focuses on maximizing results and preparing for continued progress.",
                        "disciplineSpecifics": {},
                    },
                ],
                "exampleSessions": [
                    {
                        "SessionName": f"Core {primary_discipline} Session",
                        "sessionType": primary_discipline,
                        "Duration": "45-60 minutes",
                        "SessionDescription": f"A fundamental {primary_discipline.lower()} session focusing on key movements and techniques.",
                    },
                    {
                        "SessionName": f"Progressive {primary_discipline} Session",
                        "sessionType": primary_discipline,
                        "Duration": "60-75 minutes",
                        "SessionDescription": f"An advanced {primary_discipline.lower()} session with increased intensity and complexity.",
                    },
                ],
            }

            # Try to preserve any valid data from the failed attempt
            if isinstance(failed_plan_data, dict):
                # Safely extract valid fields from failed plan
                safe_fields = ["name", "description", "duration", "rationale"]
                for field in safe_fields:
                    if (
                        field in failed_plan_data
                        and failed_plan_data[field]
                        and isinstance(failed_plan_data[field], str)
                    ):
                        fallback_plan_data[field] = failed_plan_data[field]

            logger.info(
                f"[Node: generatePlanNode] Created fallback plan: {fallback_plan_data['name']}"
            )
            return PlanDetails(**fallback_plan_data)

        except Exception as e:
            logger.error(f"[Node: generatePlanNode] Error creating fallback plan: {e}")
            # Return absolute minimal plan if everything fails
            return PlanDetails(
                planId=str(uuid.uuid4()),
                name="Basic Fitness Plan",
                description="A simple fitness plan to get you started.",
                duration="8 weeks",
                level="Beginner",
                planType="Strength",
                disciplines=["Strength"],
                rationale="A basic plan designed to establish fitness fundamentals.",
                phases=[
                    {
                        "phaseName": "Getting Started",
                        "duration": "8 weeks",
                        "weeks": "1-8",
                        "focus": "Basic fitness",
                        "description": "Focus on establishing basic fitness habits.",
                        "rationale": "Starting with fundamentals is key to long-term success.",
                        "disciplineSpecifics": {},
                    }
                ],
                exampleSessions=[
                    {
                        "SessionName": "Basic Workout",
                        "sessionType": "Strength",
                        "Duration": "30 minutes",
                        "SessionDescription": "A simple workout to get started.",
                    }
                ],
            )

    async def _call_llm_with_timeout_and_retry(
        self, model_with_structured_output, system_prompt: str
    ) -> Dict[str, Any]:
        """Call LLM with timeout handling and retry logic"""
        for attempt in range(self.max_retries + 1):
            try:
                logger.info(
                    f"[Node: generatePlanNode] LLM call attempt {attempt + 1}/{self.max_retries + 1}"
                )

                # Use asyncio.wait_for to add timeout
                generated_plan_object = await asyncio.wait_for(
                    model_with_structured_output.ainvoke(
                        [SystemMessage(content=system_prompt)]
                    ),
                    timeout=self.llm_timeout_seconds,
                )

                logger.info("[Node: generatePlanNode] LLM call completed successfully")
                return generated_plan_object

            except asyncio.TimeoutError:
                logger.warning(
                    f"[Node: generatePlanNode] LLM call timed out after {self.llm_timeout_seconds} seconds (attempt {attempt + 1})"
                )
                if attempt < self.max_retries:
                    logger.info(
                        f"[Node: generatePlanNode] Retrying LLM call... ({attempt + 1}/{self.max_retries})"
                    )
                    await asyncio.sleep(2)  # Brief delay before retry
                else:
                    logger.error(
                        "[Node: generatePlanNode] All LLM call attempts failed due to timeout"
                    )
                    raise

            except Exception as e:
                logger.error(
                    f"[Node: generatePlanNode] LLM call failed (attempt {attempt + 1}): {e}"
                )
                if attempt < self.max_retries:
                    logger.info(
                        f"[Node: generatePlanNode] Retrying LLM call... ({attempt + 1}/{self.max_retries})"
                    )
                    await asyncio.sleep(2)  # Brief delay before retry
                else:
                    logger.error(
                        "[Node: generatePlanNode] All LLM call attempts failed"
                    )
                    raise

        # This should never be reached, but just in case
        raise Exception("Maximum retry attempts exceeded")

    async def _get_mongo_client(self):
        """Get or create MongoDB client for persistence"""
        if not self.mongodb_uri:
            logger.warning("[MongoDB] No MONGODB_URI configured, skipping persistence")
            return None

        if not self._mongo_client:
            self._mongo_client = MongoClient(self.mongodb_uri)

        return self._mongo_client

    async def _save_onboarding_sidebar_data(
        self, user_id: str, sidebar_data: SidebarStateData, thread_id: str = None
    ):
        """Persist onboarding sidebar data to MongoDB user document"""
        try:
            if self.dev_mode:
                logger.info(
                    f"[DEV MODE] Skipping save of onboarding data for user: {user_id}"
                )
                return

            if not user_id or not sidebar_data:
                logger.warning(
                    "[MongoDB] Missing user_id or sidebar_data for persistence"
                )
                return

            mongo_client = await self._get_mongo_client()
            if not mongo_client:
                return

            # Convert Pydantic model to dict for MongoDB storage
            sidebar_dict = (
                sidebar_data.model_dump()
                if hasattr(sidebar_data, "model_dump")
                else sidebar_data.model_dump()
            )

            # Add timestamp for resume logic
            sidebar_dict["last_updated"] = datetime.now().isoformat()

            # Add thread_id if provided for resume functionality
            if thread_id:
                sidebar_dict["thread_id"] = thread_id

            logger.info(
                f"[MongoDB] [generatePlan] Saving onboarding sidebar data for user: {user_id}, thread_id: {thread_id}"
            )

            db = mongo_client["AthleaUserData"]
            users_collection = db["users"]

            # Update user document with onboarding sidebar data and thread_id
            update_data = {
                "onboarding_sidebar_data": sidebar_dict,
                "onboarding_last_updated": sidebar_dict["last_updated"],
            }

            # Also save thread_id at the top level for easy access
            if thread_id:
                update_data["onboarding_thread_id"] = thread_id

            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: users_collection.update_one(
                    {"user_id": user_id},
                    {"$set": update_data},
                    upsert=True,
                ),
            )

            if result.modified_count > 0 or result.upserted_id:
                logger.info(
                    f"[MongoDB] [generatePlan] ✅ Successfully saved onboarding data for user: {user_id}"
                )
            else:
                logger.warning(
                    f"[MongoDB] [generatePlan] No changes made when saving onboarding data for user: {user_id}"
                )

        except Exception as error:
            logger.error(
                f"[MongoDB] [generatePlan] Error saving onboarding sidebar data for user {user_id}: {error}"
            )

    async def _save_onboarding_stage(
        self, user_id: str, onboarding_stage: str, thread_id: str = None
    ):
        """Save the main onboarding stage to MongoDB"""
        try:
            if self.dev_mode:
                logger.info(
                    f"[DEV MODE] Skipping save of onboarding stage for user: {user_id}"
                )
                return

            if not user_id or not onboarding_stage:
                logger.warning(
                    "[MongoDB] Missing user_id or onboarding_stage for persistence"
                )
                return

            mongo_client = await self._get_mongo_client()
            if not mongo_client:
                return

            logger.info(
                f"[MongoDB] [generatePlan] Saving onboarding stage '{onboarding_stage}' for user: {user_id}"
            )

            db = mongo_client["AthleaUserData"]
            users_collection = db["users"]

            # Update user document with onboarding stage
            update_data = {
                "onboarding_stage": onboarding_stage,
                "onboarding_stage_updated": datetime.now().isoformat(),
                "onboarding_completed": (
                    "yes" if onboarding_stage == "complete" else "no"
                ),
            }

            # Also save thread_id if provided
            if thread_id:
                update_data["onboarding_thread_id"] = thread_id

            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: users_collection.update_one(
                    {"user_id": user_id},
                    {"$set": update_data},
                    upsert=True,
                ),
            )

            if result.modified_count > 0 or result.upserted_id:
                logger.info(
                    f"[MongoDB] [generatePlan] ✅ Successfully saved onboarding stage '{onboarding_stage}' for user: {user_id}"
                )
            else:
                logger.warning(
                    f"[MongoDB] [generatePlan] No changes made when saving onboarding stage for user: {user_id}"
                )

        except Exception as error:
            logger.error(
                f"[MongoDB] [generatePlan] Error saving onboarding stage for user {user_id}: {error}"
            )

    async def _create_plan_overview_message(
        self,
        plan: PlanDetails,
        user_goals: str,
        summary_items: list,
        user_feedback: str = "",
        is_regeneration: bool = False,
    ) -> str:
        """Create a dynamic, conversational message about the generated plan using LLM"""
        try:
            # Extract key information from the plan for the LLM context
            plan_name = plan.name
            duration = plan.duration
            level = plan.level
            disciplines = (
                ", ".join(plan.disciplines)
                if plan.disciplines
                else "Multiple disciplines"
            )
            phase_count = len(plan.phases) if plan.phases else 0

            # Get phase details for more context
            phase_details = []
            if plan.phases:
                for i, phase in enumerate(plan.phases[:3]):  # Limit to first 3 phases
                    phase_details.append(
                        f"Phase {i+1}: {phase.phaseName} ({phase.duration}) - {phase.focus}"
                    )

            # Get example sessions for context
            session_examples = []
            if hasattr(plan, "exampleSessions") and plan.exampleSessions:
                for session in plan.exampleSessions[:2]:  # Limit to first 2 sessions
                    session_examples.append(
                        f"{session.get('SessionName', 'Session')}: {session.get('sessionType', 'Training')}"
                    )

            # Create dynamic prompt for LLM
            regeneration_context = ""
            if is_regeneration and user_feedback:
                regeneration_context = f"""

IMPORTANT - THIS IS A PLAN REGENERATION:
The user provided feedback on their previous plan: "{user_feedback}"
I've updated the plan based on their feedback. Acknowledge their feedback and highlight the specific changes made to address their concerns."""

            dynamic_prompt = f"""You are Athlea, an enthusiastic fitness coach who just {"updated" if is_regeneration else "created"} a personalized training plan for your client.{regeneration_context}

PLAN DETAILS:
- Plan Name: {plan_name}
- Duration: {duration}
- Level: {level}
- Focus Areas: {disciplines}
- Number of Phases: {phase_count}
- Plan Description: {plan.description}

PHASE BREAKDOWN:
{chr(10).join(phase_details) if phase_details else "Multiple progressive phases"}

EXAMPLE SESSIONS:
{chr(10).join(session_examples) if session_examples else "Varied training sessions"}

USER'S ORIGINAL GOALS:
{user_goals}

USER'S CONTEXT:
{chr(10).join([f"- {item.get('category', 'Info')}: {item.get('details', 'N/A')}" for item in summary_items[:5]]) if summary_items else "Based on your preferences"}

TASK: Create an enthusiastic, personalized message that:
1. {"Acknowledges their feedback and celebrates the updated plan" if is_regeneration else "Celebrates the completion of their plan"}
2. Highlights 2-3 key aspects that directly address their goals
3. Mentions specific phases or training elements that will help them
4. {"Explains what changes were made based on their feedback" if is_regeneration else "Explains why this approach will work for them"}
5. Asks if {"this version is better and if they need any other adjustments" if is_regeneration else "they'd like any adjustments or have questions"}
6. Keeps it conversational and encouraging (like talking to a friend)
7. Uses emojis appropriately but not excessively

Write 3-4 paragraphs maximum. Be specific about their plan, not generic. Make it feel personal and exciting!"""

            # Use LLM to generate dynamic response
            from langchain_core.messages import SystemMessage

            dynamic_llm = self.llm.with_config(
                {"tags": ["plan_overview_message"], "temperature": 0.8}
            )

            logger.info(
                "[Node: generatePlanNode] 🤖 Generating dynamic plan overview message..."
            )

            response = await dynamic_llm.ainvoke(
                [SystemMessage(content=dynamic_prompt)]
            )
            dynamic_message = response.content.strip()

            logger.info(
                f"[Node: generatePlanNode] ✅ Generated dynamic plan message: {len(dynamic_message)} chars"
            )

            return dynamic_message

        except Exception as e:
            logger.error(f"Error creating dynamic plan overview message: {e}")
            # Enhanced fallback message with plan details
            fallback_message = f"🎉 **Your personalized training plan is ready!**\n\n"
            fallback_message += f"I've created **{plan.name}** - a {plan.duration} {plan.level.lower()} program focusing on {', '.join(plan.disciplines) if plan.disciplines else 'your goals'}.\n\n"
            fallback_message += f"{plan.description}\n\n"
            fallback_message += "Your complete plan is now available in the sidebar with detailed phases and example sessions. What do you think? Any adjustments you'd like me to make? 🚀"
            return fallback_message

    async def __call__(self, state: OnboardingState) -> Dict[str, Any]:
        """Generate a personalized fitness plan based on gathered information"""
        logger.info("[Node: generatePlanNode] 🚀 Starting plan generation")

        try:
            # Handle both dict and SidebarStateData cases
            sidebar_raw = state.get("sidebar_data", {})
            if isinstance(sidebar_raw, SidebarStateData):
                current_sidebar_data = sidebar_raw
            else:
                current_sidebar_data = SidebarStateData(**sidebar_raw)

            logger.info(
                f"[Node: generatePlanNode] Processing sidebar data with {len(current_sidebar_data.summary_items or [])} summary items"
            )

            # Bind the JSON schema to the LLM
            model_with_structured_output = self.llm.with_structured_output(
                PLAN_DETAILS_SCHEMA
            )

            # Format summary items for the prompt
            summary_string = ""
            if current_sidebar_data.summary_items:
                summary_string = "\n - ".join(
                    [
                        f"{item.category}: {item.details}"
                        for item in current_sidebar_data.summary_items
                    ]
                )
            else:
                summary_string = "No summary available."

            # Format user goals
            user_goals = ""
            if current_sidebar_data.goals and current_sidebar_data.goals.list:
                user_goals = ", ".join(current_sidebar_data.goals.list)
            else:
                user_goals = "Not specified"

            logger.info(f"[Node: generatePlanNode] User goals: {user_goals}")
            logger.info(
                f"[Node: generatePlanNode] Summary items count: {len(current_sidebar_data.summary_items or [])}"
            )

            # Check for user feedback from previous plan (for regeneration)
            user_feedback = ""
            is_regeneration = False
            messages = state.get("messages", [])

            # Check if this is a regeneration by looking for existing plan in sidebar
            existing_plan = None
            if current_sidebar_data.generated_plan:
                existing_plan = current_sidebar_data.generated_plan
                is_regeneration = True
                logger.info(
                    "[Node: generatePlanNode] Detected existing plan - this is a regeneration"
                )

            if messages and is_regeneration:
                # Look for the last human message that contains feedback for regeneration
                for msg in reversed(messages):
                    if hasattr(msg, "type") and msg.type == "human":
                        content = msg.content.strip()
                        # Skip simple approval messages - these don't contain useful feedback
                        approval_phrases = [
                            "yes",
                            "yeah",
                            "yep",
                            "looks good",
                            "sounds good",
                            "perfect",
                            "great",
                            "approve",
                            "approved",
                            "love it",
                            "like it",
                            "good",
                            "okay",
                            "ok",
                            "fantastic",
                            "awesome",
                            "excellent",
                        ]

                        # Check if this is actually feedback (not just approval)
                        adjustment_indicators = [
                            "change",
                            "adjust",
                            "modify",
                            "different",
                            "more",
                            "less",
                            "harder",
                            "easier",
                            "intense",
                            "days",
                            "focus",
                            "add",
                            "remove",
                            "instead",
                            "can you",
                            "make it",
                            "i need",
                            "i want",
                        ]

                        content_lower = content.lower()
                        has_approval = any(
                            phrase in content_lower for phrase in approval_phrases
                        )
                        has_adjustment = any(
                            indicator in content_lower
                            for indicator in adjustment_indicators
                        )

                        # Only use as feedback if it contains adjustment language and isn't just approval
                        if has_adjustment and not (has_approval and not has_adjustment):
                            user_feedback = content
                            logger.info(
                                f"[Node: generatePlanNode] Found user feedback for regeneration: {user_feedback}"
                            )
                        break

            # Get the planning prompt template (loads lazily if needed)
            planning_template = await self._get_planning_prompt_template()

            # Enhance the system prompt with user feedback if this is a regeneration
            feedback_instruction = ""
            if user_feedback:
                feedback_instruction = f"\n\nIMPORTANT: This is a plan regeneration based on user feedback: '{user_feedback}'\nPlease incorporate this feedback and adjust the plan accordingly. Pay special attention to the user's specific requests for changes."

            # Prepare the system prompt using the template
            system_prompt = (
                planning_template.format(
                    user_goals=user_goals, summary_string=summary_string
                )
                + feedback_instruction
            )

            logger.info(
                "[Node: generatePlanNode] 🤖 Calling LLM for structured plan generation"
            )

            # Generate the structured plan with timeout and retry
            generated_plan_object = await self._call_llm_with_timeout_and_retry(
                model_with_structured_output, system_prompt
            )

            logger.info(
                "[Node: generatePlanNode] ✅ Plan generation complete. Processing plan data..."
            )

            # The tool call returns a Pydantic object, convert to dict for serialization
            plan_data = generated_plan_object

            # Validate and fix the plan data before creating PlanDetails
            plan_data = self._validate_and_fix_plan_data(plan_data)

            # Try to create PlanDetails with validation - use fallback if it fails
            try:
                final_plan = PlanDetails(**plan_data)
                logger.info(
                    f"[Node: generatePlanNode] ✅ Successfully validated plan: {final_plan.name}"
                )
            except Exception as validation_error:
                logger.warning(
                    f"[Node: generatePlanNode] Plan validation failed: {validation_error}"
                )
                logger.info(
                    "[Node: generatePlanNode] 🔄 Attempting fallback plan generation..."
                )

                # Create a simplified fallback plan
                final_plan = self._create_fallback_plan(current_sidebar_data, plan_data)
                logger.info(
                    f"[Node: generatePlanNode] ✅ Generated fallback plan: {final_plan.name}"
                )

            # Serialize the plan
            try:
                serialized_plan = final_plan.model_dump()
                logger.info("[Node: generatePlanNode] ✅ Plan serialization successful")
            except Exception as e:
                logger.error(f"[Node: generatePlanNode] Plan serialization failed: {e}")
                # Use fallback plan if serialization fails
                final_plan = self._create_fallback_plan(current_sidebar_data)
                serialized_plan = final_plan.model_dump()

            # Create dynamic, conversational success message with plan overview
            # Prepare summary items as dicts for the LLM
            summary_items_for_llm = []
            if current_sidebar_data.summary_items:
                summary_items_for_llm = [
                    {"category": item.category, "details": item.details}
                    for item in current_sidebar_data.summary_items
                ]

            plan_overview = await self._create_plan_overview_message(
                final_plan,
                user_goals,
                summary_items_for_llm,
                user_feedback,
                is_regeneration,
            )
            summary_message = AIMessage(content=plan_overview)

            # Prepare the updated sidebar data including the Pydantic plan (for internal use)
            try:
                updated_sidebar_data = SidebarStateData(
                    current_stage="plan_review",  # Changed to "plan_review" for user confirmation
                    goals=current_sidebar_data.goals,
                    summary_items=current_sidebar_data.summary_items,
                    generated_plan=final_plan,  # Use Pydantic plan for internal state
                    sport_suggestions=current_sidebar_data.sport_suggestions,
                    selected_sport=current_sidebar_data.selected_sport,
                    selected_sports=current_sidebar_data.selected_sports,
                )

                # Create serialized sidebar data for frontend
                serialized_sidebar_data = updated_sidebar_data.model_dump()
                serialized_sidebar_data["generatedPlan"] = serialized_plan

                logger.info(
                    "[Node: generatePlanNode] ✅ Sidebar data prepared successfully"
                )
            except Exception as e:
                logger.error(
                    f"[Node: generatePlanNode] Error preparing sidebar data: {e}"
                )
                # Use minimal sidebar data if preparation fails
                serialized_sidebar_data = {
                    "current_stage": "plan_ready",  # Changed from "complete" to "plan_ready"
                    "generatedPlan": serialized_plan,
                    "goals": (
                        current_sidebar_data.goals.model_dump()
                        if current_sidebar_data.goals
                        else {"exists": False, "list": []}
                    ),
                    "summary_items": [
                        item.model_dump()
                        for item in (current_sidebar_data.summary_items or [])
                    ],
                }
                updated_sidebar_data = SidebarStateData(**serialized_sidebar_data)

            # Save to MongoDB (don't let this block the response)
            user_id = state.get("user_id")
            if user_id and updated_sidebar_data:
                try:
                    thread_id = state.get("thread_id")
                    logger.info(
                        f"[MongoDB] [generatePlan] 💾 Saving complete stage for user: {user_id}"
                    )

                    # Save both sidebar data and main onboarding stage
                    await self._save_onboarding_sidebar_data(
                        user_id, updated_sidebar_data, thread_id
                    )
                    await self._save_onboarding_stage(user_id, "plan_ready", thread_id)

                    logger.info(
                        f"[MongoDB] [generatePlan] ✅ Successfully persisted complete stage and plan data"
                    )
                except Exception as e:
                    logger.warning(
                        f"[MongoDB] [generatePlan] ⚠️  Error saving to MongoDB (continuing anyway): {e}"
                    )

            # Return the state update with serialized plan for frontend compatibility
            result = {
                "messages": [summary_message],
                "generated_plan": serialized_plan,
                "sidebar_data": serialized_sidebar_data,
                "onboarding_stage": "plan_ready",  # Changed from "complete" to "plan_ready"
            }

            logger.info(
                "[Node: generatePlanNode] 🎉 Plan generation completed successfully"
            )
            return result

        except Exception as error:
            logger.error(
                f"[Node: generatePlanNode] ❌ Critical error in plan generation: {error}"
            )

            # Create a robust fallback response
            try:
                # Try to get current sidebar data
                sidebar_raw = state.get("sidebar_data", {})
                if isinstance(sidebar_raw, SidebarStateData):
                    current_sidebar_data = sidebar_raw
                else:
                    current_sidebar_data = SidebarStateData(**sidebar_raw)

                # Create fallback plan
                fallback_plan = self._create_fallback_plan(current_sidebar_data)
                fallback_serialized = fallback_plan.model_dump()

                # Create fallback sidebar data
                fallback_sidebar_data = {
                    "current_stage": "plan_ready",  # Changed from "complete" to "plan_ready"
                    "generatedPlan": fallback_serialized,
                    "goals": (
                        current_sidebar_data.goals.model_dump()
                        if current_sidebar_data.goals
                        else {"exists": False, "list": []}
                    ),
                    "summary_items": [
                        item.model_dump()
                        for item in (current_sidebar_data.summary_items or [])
                    ],
                }

                # Create dynamic success message for fallback
                try:
                    # Try to create dynamic message even for fallback
                    fallback_summary_items = []
                    if current_sidebar_data.summary_items:
                        fallback_summary_items = [
                            {"category": item.category, "details": item.details}
                            for item in current_sidebar_data.summary_items
                        ]

                    fallback_user_goals = ""
                    if current_sidebar_data.goals and current_sidebar_data.goals.list:
                        fallback_user_goals = ", ".join(current_sidebar_data.goals.list)
                    else:
                        fallback_user_goals = "Your fitness goals"

                    fallback_plan_overview = await self._create_plan_overview_message(
                        fallback_plan,
                        fallback_user_goals,
                        fallback_summary_items,
                        "",
                        False,
                    )
                    success_message = AIMessage(content=fallback_plan_overview)
                except Exception as msg_error:
                    logger.warning(
                        f"Failed to create dynamic fallback message: {msg_error}"
                    )
                    # Ultimate fallback message
                    success_message = AIMessage(
                        content="🎉 Your training plan has been generated! I've created a comprehensive plan tailored to your goals. Check it out in the sidebar and let me know what you think - any adjustments you'd like me to make?"
                    )

                logger.info(
                    "[Node: generatePlanNode] ✅ Fallback plan generated successfully"
                )

                return {
                    "messages": [success_message],
                    "generated_plan": fallback_serialized,
                    "sidebar_data": fallback_sidebar_data,
                    "onboarding_stage": "plan_ready",  # Changed from "complete" to "plan_ready"
                }

            except Exception as fallback_error:
                logger.error(
                    f"[Node: generatePlanNode] ❌ Even fallback failed: {fallback_error}"
                )

                # Absolute last resort - minimal error state
                error_message = AIMessage(
                    content="I apologize, but I encountered an error while generating your plan. Please try again, and I'll help you create a personalized training plan."
                )

                return {
                    "messages": [error_message],
                    "sidebar_data": {"current_stage": "error"},
                    "onboarding_stage": "error",
                }


# Create the node instance
generate_plan_node = GeneratePlanNode()
