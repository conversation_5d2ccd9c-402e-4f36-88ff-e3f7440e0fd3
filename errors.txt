
2025-06-23 16:04:26,998 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [extractSports] Structured response: {'sports': ['Running', 'Cycling', 'Strength Training'], 'found': True}
2025-06-23 16:04:26,998 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [extractSports] Extracted 3 validated sports: ['Running', 'Cycling', 'Strength Training']
2025-06-23 16:04:26,998 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 1390 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chain_stream', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] LLM Extraction - Goals: {'goals': ['Lose 20 pounds', 'Improve cardiovascular endurance', 'Build functional strength for daily activities', 'Run a 10K race in 6 months'], 'exists': True}
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] LLM Extraction - Summary Items: [SummaryItem(category='Goals', details='Lose 20 pounds, improve cardiovascular endurance, build functional strength for daily activities, run a 10K in 6 months', isImportant=False), SummaryItem(category='Sports/Activities', details='Running, Cycling, Strength Training', isImportant=False), SummaryItem(category='Experience Level', details='Beginner runner, some basic strength training experience, intermediate fitness level', isImportant=False), SummaryItem(category='Time Availability', details='Work out 4-5 times per week, mornings around 6 AM', isImportant=False), SummaryItem(category='Physical Considerations', details='Minor knee issues from playing soccer in college, need to avoid high-impact activities', isImportant=False), SummaryItem(category='Preferences', details='Lower intensity steady workouts, gradual progression, focus on functional strength', isImportant=False), SummaryItem(category='Equipment Access', details='Full gym equipment, resistance bands at home, running shoes, basic workout clothes, outdoor activities on weekends', isImportant=False)]
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] LLM Extraction - Sports: ['Running', 'Cycling', 'Strength Training']
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] *** EXTRACTION COMPLETE ***
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Added new goal: Lose 20 pounds
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Added new goal: Improve cardiovascular endurance
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Added new goal: Build functional strength for daily activities
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Added new goal: Run a 10K race in 6 months
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Final merged goals: ['Improve at Running', 'Improve at Cycling', 'Improve at Strength Training', 'lose 20 pounds', 'improve cardiovascular endurance', 'build functional strength for daily activities', 'run a 10K race in 6 months', 'Lose 20 pounds', 'Improve cardiovascular endurance', 'Build functional strength for daily activities', 'Run a 10K race in 6 months'] (exists: True)
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] LLM detected sports: ['Running', 'Cycling', 'Strength Training']
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Sport already exists, skipping: Running
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Sport already exists, skipping: Cycling
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Sport already exists, skipping: Strength Training
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Filter] Kept 7 out of 7 summary items
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Filter] Filtered 7 down to 7 items (removed empty/not specified)
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Added new summary item: Goals
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Added new summary item: Sports/Activities
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Summary category already exists, skipping: Experience Level
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Summary category already exists, skipping: Time Availability
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Summary category already exists, skipping: Physical Considerations
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Summary category already exists, skipping: Preferences
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Summary category already exists, skipping: Equipment Access
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Selected sports exist, skipping sports summary item to avoid redundancy
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Filtered out sports summary items, remaining: 9 items
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Final merged summary items: 9 items
2025-06-23 16:04:26,999 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] *** SUGGESTION LOGIC ***
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] is_first_turn flag (for context): False
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Current selected sports count: 3
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Final decision (show_suggestions): False
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Is first turn check? False
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Added memory context to prompt
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Missing Analysis] Text-based keywords found: {'time_commitment', 'priorities_seasonality', 'experience_level', 'equipment_access', 'fitness_goals'} (not used for completion)
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Missing Analysis] Found categories: {'time_commitment', 'equipment_access', 'fitness_goals', 'experience_level'}
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Missing Analysis] Missing categories: {'priorities_seasonality'}
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Added critical missing info guidance - 80% complete, missing: {'priorities_seasonality'}
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Checking completion status before generating response...
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Missing Analysis] Text-based keywords found: {'time_commitment', 'priorities_seasonality', 'experience_level', 'equipment_access', 'fitness_goals'} (not used for completion)
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Missing Analysis] Found categories: {'time_commitment', 'equipment_access', 'fitness_goals', 'experience_level'}
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Missing Analysis] Missing categories: {'priorities_seasonality'}
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Critical Completion Check] Found categories: {'time_commitment', 'equipment_access', 'fitness_goals', 'experience_level'}
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Critical Completion Check] Missing categories: {'priorities_seasonality'}
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Critical Completion Check] Completion percentage: 80.0%
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Critical Completion Check] Essential categories present: True
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Critical Completion Check] Equipment context: True
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Critical Completion Check] Substantial conversation: True (length: 4299)
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Critical Completion Check] Meaningful summary items: True (count: 9)
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Critical Completion Check] DECISION: True
2025-06-23 16:04:27,000 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] ✅ Sufficient information detected - creating dynamic transition message
2025-06-23 16:04:27,270 - httpx - INFO - HTTP Request: POST https://ft-gpt40mini.openai.azure.com/openai/deployments/gpt-4.1-nano/chat/completions?api-version=2024-12-01-preview "HTTP/1.1 200 OK"
2025-06-23 16:04:27,271 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #1392 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'internal_processing', 'transition_message'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 9, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:9301f87f-df34-7407-c3f8-310088316db8', 'checkpoint_ns': 'gatherInfo:9301f87f-df34-7407-c3f8-310088316db8', 'ls_provider': 'azure', 'ls_model_name':
fo:9301f87f-df34-7407-c3f8-310088316db8', 'checkpoint_ns': 'gatherInfo:9301f87f-df34-7407-c3f8-310088316db8', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:28,277 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #1604 - Content preview: 'NO_CONTENT...'
2025-06-23 16:04:28,367 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Transition Message] Created dynamic message with 3 info points
2025-06-23 16:04:28,367 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Created dynamic transition message: 1048 chars
2025-06-23 16:04:28,367 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] DEBUG: current_onboarding_stage='gathering', current_sidebar_stage='plan_summary_ready'
2025-06-23 16:04:28,367 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] 🚫 PRESERVING plan_summary_ready stage to prevent loop
2025-06-23 16:04:28,367 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Goals: ['Improve at Running', 'Improve at Cycling', 'Improve at Strength Training', 'lose 20 pounds', 'improve cardiovascular endurance', 'build functional strength for daily activities', 'run a 10K race in 6 months', 'Lose 20 pounds', 'Improve cardiovascular endurance', 'Build functional strength for daily activities', 'Run a 10K race in 6 months'], SummaryItems: 9, Selected Sport: Strength Training
2025-06-23 16:04:28,367 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Setting has_enough_info: True (should_transition_to_plan: True, is_first_turn: False)
2025-06-23 16:04:28,367 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] 🚫 NOT resetting plan_summary_sent (preserving existing state)
2025-06-23 16:04:28,367 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] Saving onboarding sidebar data for user: oocR78vBmCeHSuMu7ORq1Wo6df23, thread_id: None
2025-06-23 16:04:28,415 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] Successfully saved onboarding data for user: oocR78vBmCeHSuMu7ORq1Wo6df23
2025-06-23 16:04:28,415 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Profile Update] Persisting onboarding data to training profile for user: oocR78vBmCeHSuMu7ORq1Wo6df23
2025-06-23 16:04:28,416 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Profile Update] Adding goals: Improve at Running, Improve at Cycling, Improve at Strength Training, lose 20 pounds, improve cardiovascular endurance, build functional strength for daily activities, run a 10K race in 6 months, Lose 20 pounds, Improve cardiovascular endurance, Build functional strength for daily activities, Run a 10K race in 6 months
2025-06-23 16:04:28,416 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Profile Update] Adding sports: Running, Cycling, Strength Training
2025-06-23 16:04:28,416 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Profile Update] Calling update-profile API at http://localhost:3000/api/update-profile
2025-06-23 16:04:30,364 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Profile Update] No profile update needed: No new factual information found to update the profile.
2025-06-23 16:04:30,365 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] *** FINAL UPDATES BEING RETURNED ***
2025-06-23 16:04:30,365 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.sport_suggestions: None
2025-06-23 16:04:30,365 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.selected_sports: ['Running', 'Cycling', 'Strength Training']
2025-06-23 16:04:30,365 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.selected_sport: Strength Training
2025-06-23 16:04:30,365 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.goals.list: ['Improve at Running', 'Improve at Cycling', 'Improve at Strength Training', 'lose 20 pounds', 'improve cardiovascular endurance', 'build functional strength for daily activities', 'run a 10K race in 6 months', 'Lose 20 pounds', 'Improve cardiovascular endurance', 'Build functional strength for daily activities', 'Run a 10K race in 6 months']
2025-06-23 16:04:30,365 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.goals.exists: True
2025-06-23 16:04:30,365 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.summary_items count: 9
2025-06-23 16:04:30,365 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.summary_items: ['Fitness Goals: Lose 20 pounds, improve cardiovascular endurance, build functional strength for daily activities', 'Experience Level: Beginner runner, some experience with basic strength training, current fitness level intermediate', 'Time Availability: 4-5 workouts per week, mornings around 6 AM before work', 'Equipment Access: Full gym equipment, resistance bands at home, running shoes, basic workout clothes', 'Physical Considerations: Minor knee issues from playing soccer, need to avoid high-impact activities', 'Preferences: Lower intensity steady workouts preferred over high-intensity intervals', 'Nutrition: Decent diet but high processed food intake, low protein, willing to meal prep and track food', 'Goals/Timeline: Noticeable improvements in 3 months, run a 10K in 6 months, lose 20 pounds by then', 'Goals: Lose 20 pounds, improve cardiovascular endurance, build functional strength for daily activities, run a 10K in 6 months']
2025-06-23 16:04:30,365 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.key_insights count: 0
2025-06-23 16:04:30,365 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.key_insights: {}
2025-06-23 16:04:30,365 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.current_stage: plan_summary_ready
2025-06-23 16:04:30,365 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] 🎯 RETURNING UPDATES - NODE EXECUTION COMPLETE
2025-06-23 16:04:30,365 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Updates keys: ['messages', 'sidebar_data', 'has_enough_info']
2025-06-23 16:04:30,365 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Messages count: 1
2025-06-23 16:04:30,366 - athlea_langgraph.graphs.onboarding_graph - INFO - 🧠 [ONBOARDING] information_gatherer_node completed! Result keys: ['messages', 'sidebar_data', 'has_enough_info']
2025-06-23 16:04:30,368 - athlea_langgraph.graphs.onboarding_graph - INFO - [Graph Router] _after_gather_info_router - plan_summary_sent: False, onboarding_stage: plan_summary_ready
2025-06-23 16:04:30,368 - athlea_langgraph.graphs.onboarding_graph - INFO - [Graph Router] 🚫 LOOP PREVENTION: User at plan summary stage - ending to prevent infinite loop
2025-06-23 16:04:30,368 - athlea_langgraph.graphs.onboarding_graph - INFO - [Graph Router] Plan summary confirmation should be handled by _plan_summary_confirmation_router
2025-06-23 16:04:30,373 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 1610 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chain_stream', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:30,374 - athlea_langgraph.api.main - INFO - 📋 ONBOARDING: [SIDEBAR_UPDATE] Sending sidebar data update (data changed)
2025-06-23 16:04:30,374 - athlea_langgraph.api.main - INFO - 📋 ONBOARDING: [SIDEBAR_DATA] Goals: {'exists': True, 'list': ['Improve at Running', 'Improve at Cycling', 'Improve at Strength Training', 'lose 20 pounds', 'improve cardiovascular endurance', 'build functional strength for daily activities', 'run a 10K race in 6 months', 'Lose 20 pounds', 'Improve cardiovascular endurance', 'Build functional strength for daily activities', 'Run a 10K race in 6 months']}
2025-06-23 16:04:30,374 - athlea_langgraph.api.main - INFO - 📋 ONBOARDING: [SIDEBAR_DATA] Selected Sports: ['Running', 'Cycling', 'Strength Training']
2025-06-23 16:04:30,374 - athlea_langgraph.api.main - INFO - 📋 ONBOARDING: [SIDEBAR_DATA] Summary Items Count: 9
2025-06-23 16:04:30,374 - athlea_langgraph.api.main - INFO - 📤 ONBOARDING: [SSE_SEND] Completion event sent
2025-06-23 16:04:30,374 - athlea_langgraph.api.main - INFO - 🏁 ONBOARDING: [STREAM_END] Completed streaming, total events processed: 1611
2025-06-23 16:04:30,375 - athlea_langgraph.api.main - INFO - 📊 ONBOARDING: [STREAM_SUMMARY] Agent starts sent: ['Athlea']
2025-06-23 16:04:30,375 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [TOKEN_SUMMARY] Token counts by node:
2025-06-23 16:04:30,375 - athlea_langgraph.api.main - INFO -   🎯 generatePlanSummary -> Athlea: 551 tokens
2025-06-23 16:04:30,375 - athlea_langgraph.api.main - INFO - ⏱️ ONBOARDING: [STREAM_COMPLETE] Total streaming time: 19.27s
2025-06-23 16:04:40,863 - athlea_langgraph.api.main - INFO - 🚀 ONBOARDING GET: Starting onboarding stream for user oocR78vBmCeHSuMu7ORq1Wo6df23, thread 35cb3a66-e1db-49fa-bb48-a8441798283b
2025-06-23 16:04:40,927 - athlea_langgraph.api.main - INFO - 📚 ONBOARDING GET: Searching for thread ID: 35cb3a66-e1db-49fa-bb48-a8441798283b
2025-06-23 16:04:41,290 - athlea_langgraph.api.main - INFO - 📚 ONBOARDING GET: MongoDB query result: True
2025-06-23 16:04:41,290 - athlea_langgraph.api.main - INFO - 📚 ONBOARDING GET: Document keys: ['_id', 'jobId', 'user_id', 'coachName', 'coaches', 'serviceName', 'isOnboarding', 'dateAdded', 'lastUpdated', 'planTitle', 'artifacts', 'messages', 'sourceDocuments']
2025-06-23 16:04:41,290 - athlea_langgraph.api.main - INFO - 📚 ONBOARDING GET: Found 8 messages in messages field
2025-06-23 16:04:41,290 - athlea_langgraph.api.main - INFO - 📚 ONBOARDING GET: Loaded 8 messages from MongoDB for thread 35cb3a66-e1db-49fa-bb48-a8441798283b
2025-06-23 16:04:41,290 - athlea_langgraph.api.main - INFO - 📚 ONBOARDING GET: Converted 7 messages to conversation history format
INFO:     127.0.0.1:57930 - "GET /api/onboarding?message=Perfect+that+works&userId=oocR78vBmCeHSuMu7ORq1Wo6df23&threadId=35cb3a66-e1db-49fa-bb48-a8441798283b HTTP/1.1" 200 OK
2025-06-23 16:04:41,397 - athlea_langgraph.api.main - INFO - 🚀 Ultra-fast fetch for onboarding: userId=oocR78vBmCeHSuMu7ORq1Wo6df23
2025-06-23 16:04:41,420 - athlea_langgraph.utils.advanced_user_data_cache - ERROR - ❌ Failed to initialize cache system: Error 8 connecting to athlea-redis-cache.redis.cache.windows.net:6380. nodename nor servname provided, or not known.
2025-06-23 16:04:41,420 - athlea_langgraph.utils.advanced_user_data_cache - ERROR - Error in ultra-fast fetch for oocR78vBmCeHSuMu7ORq1Wo6df23: Cache initialization failed: Error 8 connecting to athlea-redis-cache.redis.cache.windows.net:6380. nodename nor servname provided, or not known.
2025-06-23 16:04:41,420 - athlea_langgraph.api.main - INFO - ⚡ Data source for onboarding - userId oocR78vBmCeHSuMu7ORq1Wo6df23: error
2025-06-23 16:04:41,420 - athlea_langgraph.api.main - INFO - ℹ️  No training profile found for onboarding - userId: oocR78vBmCeHSuMu7ORq1Wo6df23
2025-06-23 16:04:41,420 - athlea_langgraph.api.main - INFO - ℹ️  No current plan found for onboarding - userId: oocR78vBmCeHSuMu7ORq1Wo6df23
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO - ✅ ONBOARDING: [MONGODB] Loaded onboarding data for user oocR78vBmCeHSuMu7ORq1Wo6df23: stage=plan_summary_ready, has_sidebar_data=True
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO - 📊 ONBOARDING: [SIDEBAR_DATA] current_stage=plan_summary_ready, goals_count=11, sports_count=3, summary_items_count=9
2025-06-23 16:04:41,996 - athlea_langgraph.graph_factory - INFO - [DynamicGraphFactory] Returning cached graph: onboarding_-7215252207631453580
2025-06-23 16:04:41,996 - athlea_langgraph.graph_factory - INFO - [get_graph_by_type] Created onboarding graph with memory checkpointer
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO - 🚀 ONBOARDING: [STREAM_START] Starting onboarding stream
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO - 📊 ONBOARDING: [STREAM_START] Request details: user_id=oocR78vBmCeHSuMu7ORq1Wo6df23, thread_id=35cb3a66-e1db-49fa-bb48-a8441798283b, message_length=18
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO - 🔧 ONBOARDING: [INPUT_STATE] Set onboarding_stage=plan_summary_ready, plan_summary_sent=True
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO - 🔧 ONBOARDING: [STAGE_SYNC] Synchronized sidebar current_stage to 'plan_summary_ready'
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO - 🔧 ONBOARDING: [INPUT_STATE] Added sidebar_data with current_stage=plan_summary_ready
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO - 🔧 ONBOARDING: [INPUT_STATE] Created base input state
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO - 🔧 ONBOARDING: [INPUT_STATE] User ID: oocR78vBmCeHSuMu7ORq1Wo6df23
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO - 🔧 ONBOARDING: [INPUT_STATE] Message: Perfect that works...
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO - 🔧 ONBOARDING: [INPUT_STATE] Conversation history length: 7
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO - 🔧 ONBOARDING: [INPUT_STATE] Onboarding stage: plan_summary_ready
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO - ℹ️ ONBOARDING: [INPUT_STATE] No user profile data available
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO - 🔧 ONBOARDING: [STREAM] Created input state
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO - 🗺️ ONBOARDING: [STREAM_MAPPINGS] Available node mappings:
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO -   📌 gatherInfo -> Athlea
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO -   📌 informationGathererNode -> Athlea
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO -   📌 information_gatherer -> Athlea
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO -   📌 checkCompletion -> Athlea
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO -   📌 needInput -> Athlea
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO -   📌 generatePlan -> Athlea
2025-06-23 16:04:41,996 - athlea_langgraph.api.main - INFO - 🎯 ONBOARDING: [STREAM] Starting graph execution with token streaming...
2025-06-23 16:04:41,998 - athlea_langgraph.api.main - INFO - 🚀 ONBOARDING: [AGENT_START] Chain start detected - Agent starting: 'gatherInfo' -> 'Athlea'
2025-06-23 16:04:41,998 - athlea_langgraph.api.main - INFO - 📤 ONBOARDING: [SSE_SEND] Agent start: 'Athlea' (via chain_start for 'gatherInfo')
2025-06-23 16:04:41,999 - athlea_langgraph.graphs.onboarding_graph - INFO - 🧠 [ONBOARDING] Enhanced information gathering with memory context
2025-06-23 16:04:41,999 - athlea_langgraph.graphs.onboarding_graph - INFO - 🔄 [ONBOARDING] Resetting completion flags to prevent loops
2025-06-23 16:04:41,999 - athlea_langgraph.graphs.onboarding_graph - INFO - 🔄 [ONBOARDING] Updated dict sidebar current_stage to 'gathering'
2025-06-23 16:04:42,422 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-06-23 16:04:42,427 - athlea_langgraph.services.mem0_memory_service - INFO - 🔍 Found 1 memories for query: onboarding fitness goals prefe...
2025-06-23 16:04:42,427 - athlea_langgraph.graphs.onboarding_graph - INFO - 🧠 Retrieved 1 memory items for onboarding context
2025-06-23 16:04:42,427 - athlea_langgraph.graphs.onboarding_graph - INFO - 🧠 [ONBOARDING] Calling information_gatherer_node...
2025-06-23 16:04:42,427 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Entering node.
2025-06-23 16:04:42,427 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Found 7 messages in conversation history
2025-06-23 16:04:42,427 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Using 7 history messages + 9 current messages = 16 total messages
2025-06-23 16:04:42,493 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [File Data] No uploaded files found for user oocR78vBmCeHSuMu7ORq1Wo6df23
2025-06-23 16:04:42,493 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [File Integration] No uploaded fitness data found for user oocR78vBmCeHSuMu7ORq1Wo6df23
2025-06-23 16:04:42,493 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] Loading onboarding sidebar data for user: oocR78vBmCeHSuMu7ORq1Wo6df23
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] Query result for user oocR78vBmCeHSuMu7ORq1Wo6df23: True
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] User document keys: ['_id', 'onboarding_last_updated', 'onboarding_sidebar_data']
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] Sidebar data exists: True
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] Sidebar data type: <class 'dict'>
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] Sidebar data keys: ['current_stage', 'goals', 'summary_items', 'generated_plan', 'sport_suggestions', 'selected_sport', 'selected_sports', 'weekly_plan', 'uploaded_documents', 'key_insights', 'last_updated']
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] Found goals: {'exists': True, 'list': ['Improve at Running', 'Improve at Cycling', 'Improve at Strength Training', 'lose 20 pounds', 'improve cardiovascular endurance', 'build functional strength for daily activities', 'run a 10K race in 6 months', 'Lose 20 pounds', 'Improve cardiovascular endurance', 'Build functional strength for daily activities', 'Run a 10K race in 6 months']}
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] Found selected_sports: ['Running', 'Cycling', 'Strength Training']
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] Found summary_items count: 9
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] Found existing onboarding data for user: oocR78vBmCeHSuMu7ORq1Wo6df23
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Converting sidebar_dict to SidebarStateData: type=<class 'dict'>
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_dict keys: ['current_stage', 'goals', 'summary_items', 'generated_plan', 'sport_suggestions', 'selected_sport', 'selected_sports', 'weekly_plan', 'uploaded_documents', 'key_insights', 'last_updated']
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Successfully converted to SidebarStateData, type: <class 'athlea_langgraph.states.onboarding_state.SidebarStateData'>
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] Loading existing onboarding data for resume - user: oocR78vBmCeHSuMu7ORq1Wo6df23
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] BEFORE restoration - current data summary:
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] current_sidebar_data type: <class 'dict'>
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] saved_sidebar_data type: <class 'athlea_langgraph.states.onboarding_state.SidebarStateData'>
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO -   - Goals: ['Improve at Running', 'Improve at Cycling', 'Improve at Strength Training', 'lose 20 pounds', 'improve cardiovascular endurance', 'build functional strength for daily activities', 'run a 10K race in 6 months', 'Lose 20 pounds', 'Improve cardiovascular endurance', 'Build functional strength for daily activities', 'Run a 10K race in 6 months']
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO -   - Selected sports: ['Running', 'Cycling', 'Strength Training']
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO -   - Summary items: 9
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO -   - Uploaded docs: 0
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO -   - Key insights: 0
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] AFTER restoration - complete data restored:
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] After restoration - current_sidebar_data type: <class 'athlea_langgraph.states.onboarding_state.SidebarStateData'>
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO -   - Goals: ['Improve at Running', 'Improve at Cycling', 'Improve at Strength Training', 'lose 20 pounds', 'improve cardiovascular endurance', 'build functional strength for daily activities', 'run a 10K race in 6 months', 'Lose 20 pounds', 'Improve cardiovascular endurance', 'Build functional strength for daily activities', 'Run a 10K race in 6 months']
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO -   - Selected sports: ['Running', 'Cycling', 'Strength Training']
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO -   - Summary items: 9
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO -   - Uploaded docs: 0
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO -   - Key insights: 0
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO -   - Current stage: plan_summary_ready
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] ✅ Complete onboarding data restored as single dataset
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] *** PRESERVING EXISTING SIDEBAR DATA ***
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Final current_sidebar_data type: <class 'athlea_langgraph.states.onboarding_state.SidebarStateData'>
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Existing goals: ['Improve at Running', 'Improve at Cycling', 'Improve at Strength Training', 'lose 20 pounds', 'improve cardiovascular endurance', 'build functional strength for daily activities', 'run a 10K race in 6 months', 'Lose 20 pounds', 'Improve cardiovascular endurance', 'Build functional strength for daily activities', 'Run a 10K race in 6 months']
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Existing selected sports: ['Running', 'Cycling', 'Strength Training']
2025-06-23 16:04:42,523 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Existing summary items: 9
2025-06-23 16:04:42,524 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] *** Initial Sport State ***
2025-06-23 16:04:42,524 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Sidebar - Selected Sport: Strength Training
2025-06-23 16:04:42,524 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Sidebar - Selected Sports List: ['Running', 'Cycling', 'Strength Training']
2025-06-23 16:04:42,524 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Sidebar - Has previous suggestions: False
2025-06-23 16:04:42,524 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] *** STARTING COMPLETE DATA EXTRACTION ***
2025-06-23 16:04:42,524 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Message count: 16, Is first turn: False
2025-06-23 16:04:42,524 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Has uploaded file data: False
2025-06-23 16:04:42,525 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [extractGoals] Starting structured goal extraction
2025-06-23 16:04:43,202 - httpx - INFO - HTTP Request: POST https://ft-gpt40mini.openai.azure.com/openai/deployments/gpt-4.1-nano/chat/completions?ap
del_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:44,723 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #190 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,723 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #190 - Content preview: '5...'
2025-06-23 16:04:44,723 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #191 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,723 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #191 - Content preview: ' times...'
2025-06-23 16:04:44,724 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #192 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,724 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #192 - Content preview: ' per...'
2025-06-23 16:04:44,724 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #193 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,724 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #193 - Content preview: ' week...'
2025-06-23 16:04:44,725 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #194 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,725 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #194 - Content preview: ',...'
2025-06-23 16:04:44,725 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #195 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,725 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #195 - Content preview: ' preferably...'
2025-06-23 16:04:44,726 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #196 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,726 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #196 - Content preview: ' in...'
2025-06-23 16:04:44,726 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #197 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,726 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #197 - Content preview: ' mornings...'
2025-06-23 16:04:44,727 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #198 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,727 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #198 - Content preview: ' around...'
2025-06-23 16:04:44,727 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #199 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,727 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #199 - Content preview: ' ...'
2025-06-23 16:04:44,727 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 200 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:44,727 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #200 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,727 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #200 - Content preview: '6...'
2025-06-23 16:04:44,728 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #201 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,728 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #201 - Content preview: ' AM...'
2025-06-23 16:04:44,728 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #202 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,729 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #202 - Content preview: ' before...'
2025-06-23 16:04:44,729 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #203 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,729 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #203 - Content preview: ' work...'
2025-06-23 16:04:44,729 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #204 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,729 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #204 - Content preview: '","...'
2025-06-23 16:04:44,730 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #205 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,730 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #205 - Content preview: 'is...'
2025-06-23 16:04:44,730 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #206 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,730 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #206 - Content preview: 'Important...'
2025-06-23 16:04:44,731 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #207 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,731 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #207 - Content preview: '":...'
2025-06-23 16:04:44,731 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #208 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,731 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #208 - Content preview: 'false...'
2025-06-23 16:04:44,732 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #209 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,732 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #209 - Content preview: '},{"...'
2025-06-23 16:04:44,732 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 210 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:44,732 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #210 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,732 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #210 - Content preview: 'category...'
2025-06-23 16:04:44,732 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #211 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,732 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #211 - Content preview: '":"...'
2025-06-23 16:04:44,733 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #212 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,733 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #212 - Content preview: 'Physical...'
2025-06-23 16:04:44,733 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #213 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,733 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #213 - Content preview: ' Consider...'
2025-06-23 16:04:44,869 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #214 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,869 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #214 - Content preview: 'ations...'
2025-06-23 16:04:44,869 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #215 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,869 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #215 - Content preview: '","...'
2025-06-23 16:04:44,870 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #216 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,870 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #216 - Content preview: 'details...'
2025-06-23 16:04:44,871 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #217 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,871 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #217 - Content preview: '":"...'
2025-06-23 16:04:44,871 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #218 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,871 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #218 - Content preview: 'History...'
2025-06-23 16:04:44,872 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #219 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,872 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #219 - Content preview: ' of...'
2025-06-23 16:04:44,872 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 220 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:44,872 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #220 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,872 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #220 - Content preview: ' minor...'
2025-06-23 16:04:44,872 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #221 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,872 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #221 - Content preview: ' knee...'
2025-06-23 16:04:44,873 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #222 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,873 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #222 - Content preview: ' issues...'
2025-06-23 16:04:44,873 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #223 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,873 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #223 - Content preview: ' from...'
2025-06-23 16:04:44,874 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #224 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,874 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #224 - Content preview: ' playing...'
2025-06-23 16:04:44,874 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #225 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,874 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #225 - Content preview: ' soccer...'
2025-06-23 16:04:44,875 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #226 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,875 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #226 - Content preview: ' in...'
2025-06-23 16:04:44,875 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #227 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,875 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #227 - Content preview: ' college...'
2025-06-23 16:04:44,876 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #228 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,876 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #228 - Content preview: ',...'
2025-06-23 16:04:44,876 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #229 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,876 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #229 - Content preview: ' need...'
2025-06-23 16:04:44,877 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 230 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:44,877 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #230 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,877 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #230 - Content preview: ' to...'
2025-06-23 16:04:44,877 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #231 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,877 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #231 - Content preview: ' be...'
2025-06-23 16:04:44,878 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #232 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,878 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #232 - Content preview: ' careful...'
2025-06-23 16:04:44,879 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #233 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,879 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #233 - Content preview: ' with...'
2025-06-23 16:04:44,879 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #234 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,879 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #234 - Content preview: ' high...'
2025-06-23 16:04:44,880 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #235 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,880 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #235 - Content preview: '-impact...'
2025-06-23 16:04:44,880 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #236 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,880 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #236 - Content preview: ' activities...'
2025-06-23 16:04:44,881 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #237 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,881 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #237 - Content preview: '","...'
2025-06-23 16:04:44,881 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #238 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,881 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #238 - Content preview: 'is...'
2025-06-23 16:04:44,881 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #239 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,881 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #239 - Content preview: 'Important...'
2025-06-23 16:04:44,882 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 240 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:44,882 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #240 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,882 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #240 - Content preview: '":...'
2025-06-23 16:04:44,882 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #241 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,882 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #241 - Content preview: 'true...'
2025-06-23 16:04:44,883 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #242 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,883 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #242 - Content preview: '},{"...'
2025-06-23 16:04:44,912 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #243 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,912 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #243 - Content preview: 'category...'
2025-06-23 16:04:44,912 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #244 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,912 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #244 - Content preview: '":"...'
2025-06-23 16:04:44,913 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #245 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,913 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #245 - Content preview: 'Preferences...'
2025-06-23 16:04:44,913 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #246 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,913 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #246 - Content preview: '","...'
2025-06-23 16:04:44,913 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #247 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,913 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #247 - Content preview: 'details...'
2025-06-23 16:04:44,914 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #248 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,914 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #248 - Content preview: '":"...'
2025-06-23 16:04:44,914 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #249 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,914 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #249 - Content preview: 'Lower...'
2025-06-23 16:04:44,915 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 250 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:44,915 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #250 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,915 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #250 - Content preview: ' intensity...'
2025-06-23 16:04:44,915 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #251 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,915 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #251 - Content preview: ' steady...'
2025-06-23 16:04:44,916 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #252 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,916 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #252 - Content preview: ' workouts...'
2025-06-23 16:04:44,916 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #253 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,916 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #253 - Content preview: ' over...'
2025-06-23 16:04:44,916 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #254 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:44,916 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #254 - Content preview: ' high...'
2025-06-23 16:04:45,047 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #255 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,047 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #255 - Content preview: '-int...'
2025-06-23 16:04:45,048 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #256 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,050 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #256 - Content preview: 'ensity...'
2025-06-23 16:04:45,051 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #257 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,051 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #257 - Content preview: ' intervals...'
2025-06-23 16:04:45,051 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #258 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,051 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #258 - Content preview: ',...'
2025-06-23 16:04:45,052 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #259 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,052 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #259 - Content preview: ' enjoys...'
2025-06-23 16:04:45,053 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 260 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:45,053 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #260 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,053 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #260 - Content preview: ' outdoor...'
2025-06-23 16:04:45,053 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #261 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,053 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #261 - Content preview: ' activities...'
2025-06-23 16:04:45,054 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #262 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,054 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #262 - Content preview: ' on...'
2025-06-23 16:04:45,054 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #263 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,054 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #263 - Content preview: ' weekends...'
2025-06-23 16:04:45,055 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #264 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,055 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #264 - Content preview: '","...'
2025-06-23 16:04:45,055 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #265 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,055 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #265 - Content preview: 'is...'
2025-06-23 16:04:45,056 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #266 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,056 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #266 - Content preview: 'Important...'
2025-06-23 16:04:45,056 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #267 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,056 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #267 - Content preview: '":...'
2025-06-23 16:04:45,124 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #268 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,125 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #268 - Content preview: 'false...'
2025-06-23 16:04:45,125 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #269 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,126 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #269 - Content preview: '},{"...'
2025-06-23 16:04:45,126 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 270 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:45,126 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #270 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,126 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #270 - Content preview: 'category...'
2025-06-23 16:04:45,127 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #271 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,127 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #271 - Content preview: '":"...'
2025-06-23 16:04:45,127 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #272 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,127 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #272 - Content preview: 'Equipment...'
2025-06-23 16:04:45,128 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #273 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,128 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #273 - Content preview: ' Access...'
2025-06-23 16:04:45,129 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #274 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,129 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #274 - Content preview: '","...'
2025-06-23 16:04:45,129 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #275 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,129 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #275 - Content preview: 'details...'
2025-06-23 16:04:45,130 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #276 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,130 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #276 - Content preview: '":"...'
2025-06-23 16:04:45,131 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #277 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,131 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #277 - Content preview: 'Access...'
2025-06-23 16:04:45,131 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #278 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,131 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #278 - Content preview: ' to...'
2025-06-23 16:04:45,132 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #279 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,132 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #279 - Content preview: ' a...'
2025-06-23 16:04:45,132 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 280 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:45,132 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #280 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,132 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #280 - Content preview: ' fully...'
2025-06-23 16:04:45,133 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #281 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,133 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #281 - Content preview: ' equipped...'
2025-06-23 16:04:45,134 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #282 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,134 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #282 - Content preview: ' gym...'
2025-06-23 16:04:45,134 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #283 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,134 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #283 - Content preview: ',...'
2025-06-23 16:04:45,141 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #284 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,141 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #284 - Content preview: ' running...'
2025-06-23 16:04:45,143 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #285 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,143 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #285 - Content preview: ' shoes...'
2025-06-23 16:04:45,144 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #286 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,144 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #286 - Content preview: ',...'
2025-06-23 16:04:45,145 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #287 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,146 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #287 - Content preview: ' basic...'
2025-06-23 16:04:45,147 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #288 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,147 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #288 - Content preview: ' workout...'
2025-06-23 16:04:45,149 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #289 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,149 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #289 - Content preview: ' clothes...'
2025-06-23 16:04:45,150 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 290 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:45,151 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #290 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,151 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #290 - Content preview: ',...'
2025-06-23 16:04:45,151 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #291 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,151 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #291 - Content preview: ' resistance...'
2025-06-23 16:04:45,152 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #292 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,152 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #292 - Content preview: ' bands...'
2025-06-23 16:04:45,153 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #293 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,153 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #293 - Content preview: ' at...'
2025-06-23 16:04:45,154 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #294 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,154 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #294 - Content preview: ' home...'
2025-06-23 16:04:45,155 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #295 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,155 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #295 - Content preview: ' for...'
2025-06-23 16:04:45,155 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #296 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,155 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #296 - Content preview: ' days...'
2025-06-23 16:04:45,178 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #297 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,178 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #297 - Content preview: ' away...'
2025-06-23 16:04:45,179 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #298 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,179 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #298 - Content preview: ' from...'
2025-06-23 16:04:45,179 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #299 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,179 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #299 - Content preview: ' gym...'
2025-06-23 16:04:45,179 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 300 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:45,179 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #300 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,179 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #300 - Content preview: '","...'
2025-06-23 16:04:45,180 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #301 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,180 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #301 - Content preview: 'is...'
2025-06-23 16:04:45,180 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #302 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,180 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #302 - Content preview: 'Important...'
2025-06-23 16:04:45,181 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #303 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,181 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #303 - Content preview: '":...'
2025-06-23 16:04:45,181 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #304 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,181 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #304 - Content preview: 'true...'
2025-06-23 16:04:45,182 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #305 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,182 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #305 - Content preview: '}...'
2025-06-23 16:04:45,182 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #306 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,182 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #306 - Content preview: ']}...'
2025-06-23 16:04:45,183 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #307 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,183 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #307 - Content preview: 'NO_CONTENT...'
2025-06-23 16:04:45,183 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #308 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,183 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #308 - Content preview: 'NO_CONTENT...'
2025-06-23 16:04:45,279 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #309 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSummary', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,279 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #309 - Content preview: 'NO_CONTENT...'
2025-06-23 16:04:45,280 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 310 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:45,281 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [extractSummary] Extracted summaryList: [{'category': 'Sports/Activities', 'details': 'Running, Cycling, Strength Training', 'isImportant': True}, {'category': 'Fitness Goals', 'details': 'Lose 20 pounds, improve cardiovascular endurance, build functional strength for daily activities, run a 10K in 6 months', 'isImportant': True}, {'category': 'Experience Level', 'details': 'Beginner runner, some experience with basic strength training from gym classes, current fitness level intermediate, can run about 2 miles without stopping, struggles with hills, can do basic exercises like push-ups, squats, and planks', 'isImportant': True}, {'category': 'Time Availability', 'details': 'Work out 4-5 times per week, preferably in mornings around 6 AM before work', 'isImportant': False}, {'category': 'Physical Considerations', 'details': 'History of minor knee issues from playing soccer in college, need to be careful with high-impact activities', 'isImportant': True}, {'category': 'Preferences', 'details': 'Lower intensity steady workouts over high-intensity intervals, enjoys outdoor activities on weekends', 'isImportant': False}, {'category': 'Equipment Access', 'details': 'Access to a fully equipped gym, running shoes, basic workout clothes, resistance bands at home for days away from gym', 'isImportant': True}]
2025-06-23 16:04:45,282 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [extractSports] Starting structured sport extraction from conversation
2025-06-23 16:04:45,748 - httpx - INFO - HTTP Request: POST https://ft-gpt40mini.openai.azure.com/openai/deployments/gpt-4.1-nano/chat/completions?api-version=2024-12-01-preview "HTTP/1.1 200 OK"
2025-06-23 16:04:45,752 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #318 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,752 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #318 - Content preview: 'NO_CONTENT...'
2025-06-23 16:04:45,855 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #319 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,855 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #319 - Content preview: '{"...'
2025-06-23 16:04:45,856 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 320 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:45,856 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #320 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,856 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #320 - Content preview: 'sports...'
2025-06-23 16:04:45,857 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #321 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,857 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #321 - Content preview: '":...'
2025-06-23 16:04:45,858 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #322 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,858 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #322 - Content preview: ' ["...'
2025-06-23 16:04:45,858 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #323 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,859 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #323 - Content preview: 'Running...'
2025-06-23 16:04:45,859 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #324 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,859 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #324 - Content preview: '",...'
2025-06-23 16:04:45,860 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #325 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,860 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #325 - Content preview: ' "...'
2025-06-23 16:04:45,861 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #326 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,861 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #326 - Content preview: 'Cycl...'
2025-06-23 16:04:45,862 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #327 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,862 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #327 - Content preview: 'ing...'
2025-06-23 16:04:45,863 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #328 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,863 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #328 - Content preview: '",...'
2025-06-23 16:04:45,863 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #329 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,863 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #329 - Content preview: ' "...'
2025-06-23 16:04:45,864 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 330 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:45,864 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #330 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,864 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #330 - Content preview: 'Strength...'
2025-06-23 16:04:45,865 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #331 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,865 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #331 - Content preview: ' Training...'
2025-06-23 16:04:45,866 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #332 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,866 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #332 - Content preview: '"],...'
2025-06-23 16:04:45,867 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #333 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,867 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #333 - Content preview: ' "...'
2025-06-23 16:04:45,868 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #334 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,868 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #334 - Content preview: 'found...'
2025-06-23 16:04:45,869 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #335 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,869 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #335 - Content preview: '":...'
2025-06-23 16:04:45,869 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #336 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,869 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #336 - Content preview: ' true...'
2025-06-23 16:04:45,870 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #337 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,870 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #337 - Content preview: '}...'
2025-06-23 16:04:45,870 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #338 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,870 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #338 - Content preview: 'NO_CONTENT...'
2025-06-23 16:04:45,870 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #339 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,870 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #339 - Content preview: 'NO_CONTENT...'
2025-06-23 16:04:45,952 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 340 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:45,952 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #340 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'extractSports', 'internal_processing'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:45,952 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #340 - Content preview: 'NO_CONTENT...'
2025-06-23 16:04:45,956 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [extractSports] Structured response: {'sports': ['Running', 'Cycling', 'Strength Training'], 'found': True}
2025-06-23 16:04:45,956 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [extractSports] Extracted 3 validated sports: ['Running', 'Cycling', 'Strength Training']
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] LLM Extraction - Goals: {'goals': ['lose 20 pounds', 'improve cardiovascular endurance', 'build functional strength for daily activities', 'run a 10K race in 6 months'], 'exists': True}
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] LLM Extraction - Summary Items: [SummaryItem(category='Sports/Activities', details='Running, Cycling, Strength Training', isImportant=False), SummaryItem(category='Fitness Goals', details='Lose 20 pounds, improve cardiovascular endurance, build functional strength for daily activities, run a 10K in 6 months', isImportant=False), SummaryItem(category='Experience Level', details='Beginner runner, some experience with basic strength training from gym classes, current fitness level intermediate, can run about 2 miles without stopping, struggles with hills, can do basic exercises like push-ups, squats, and planks', isImportant=False), SummaryItem(category='Time Availability', details='Work out 4-5 times per week, preferably in mornings around 6 AM before work', isImportant=False), SummaryItem(category='Physical Considerations', details='History of minor knee issues from playing soccer in college, need to be careful with high-impact activities', isImportant=False), SummaryItem(category='Preferences', details='Lower intensity steady workouts over high-intensity intervals, enjoys outdoor activities on weekends', isImportant=False), SummaryItem(category='Equipment Access', details='Access to a fully equipped gym, running shoes, basic workout clothes, resistance bands at home for days away from gym', isImportant=False)]
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] LLM Extraction - Sports: ['Running', 'Cycling', 'Strength Training']
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] *** EXTRACTION COMPLETE ***
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Final merged goals: ['Improve at Running', 'Improve at Cycling', 'Improve at Strength Training', 'lose 20 pounds', 'improve cardiovascular endurance', 'build functional strength for daily activities', 'run a 10K race in 6 months', 'Lose 20 pounds', 'Improve cardiovascular endurance', 'Build functional strength for daily activities', 'Run a 10K race in 6 months'] (exists: True)
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] LLM detected sports: ['Running', 'Cycling', 'Strength Training']
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Sport already exists, skipping: Running
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Sport already exists, skipping: Cycling
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Sport already exists, skipping: Strength Training
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Filter] Kept 7 out of 7 summary items
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Filter] Filtered 7 down to 7 items (removed empty/not specified)
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Added new summary item: Sports/Activities
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Summary category already exists, skipping: Fitness Goals
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Summary category already exists, skipping: Experience Level
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Summary category already exists, skipping: Time Availability
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Summary category already exists, skipping: Physical Considerations
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Summary category already exists, skipping: Preferences
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Summary category already exists, skipping: Equipment Access
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Selected sports exist, skipping sports summary item to avoid redundancy
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Filtered out sports summary items, remaining: 9 items
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] Final merged summary items: 9 items
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] *** SUGGESTION LOGIC ***
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] is_first_turn flag (for context): False
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Current selected sports count: 3
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Final decision (show_suggestions): False
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Is first turn check? False
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Added memory context to prompt
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Missing Analysis] Text-based keywords found: {'time_commitment', 'priorities_seasonality', 'experience_level', 'equipment_access', 'fitness_goals'} (not used for completion)
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Missing Analysis] Found categories: {'time_commitment', 'equipment_access', 'fitness_goals', 'experience_level'}
2025-06-23 16:04:45,957 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Missing Analysis] Missing categories: {'priorities_seasonality'}
2025-06-23 16:04:45,958 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Added critical missing info guidance - 80% complete, missing: {'priorities_seasonality'}
2025-06-23 16:04:45,958 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Checking completion status before generating response...
2025-06-23 16:04:45,958 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Missing Analysis] Text-based keywords found: {'time_commitment', 'priorities_seasonality', 'experience_level', 'equipment_access', 'fitness_goals'} (not used for completion)
2025-06-23 16:04:45,958 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Missing Analysis] Found categories: {'time_commitment', 'equipment_access', 'fitness_goals', 'experience_level'}
2025-06-23 16:04:45,958 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Missing Analysis] Missing categories: {'priorities_seasonality'}
2025-06-23 16:04:45,958 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Critical Completion Check] Found categories: {'time_commitment', 'equipment_access', 'fitness_goals', 'experience_level'}
2025-06-23 16:04:45,958 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Critical Completion Check] Missing categories: {'priorities_seasonality'}
2025-06-23 16:04:45,958 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Critical Completion Check] Completion percentage: 80.0%
2025-06-23 16:04:45,958 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Critical Completion Check] Essential categories present: True
2025-06-23 16:04:45,958 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Critical Completion Check] Equipment context: True
2025-06-23 16:04:45,958 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Critical Completion Check] Substantial conversation: True (length: 7247)
2025-06-23 16:04:45,958 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Critical Completion Check] Meaningful summary items: True (count: 9)
2025-06-23 16:04:45,958 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Critical Completion Check] DECISION: True
2025-06-23 16:04:45,958 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] ✅ Sufficient information detected - creating dynamic transition message
2025-06-23 16:04:46,276 - httpx - INFO - HTTP Request: POST https://ft-gpt40mini.openai.azure.com/openai/deployments/gpt-4.1-nano/chat/completions?api-version=2024-12-01-preview "HTTP/1.1 200 OK"
2025-06-23 16:04:46,278 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #347 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'internal_processing', 'transition_message'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 'ls_model_type': 'chat', 'ls_temperature': 0.3}
2025-06-23 16:04:46,278 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [CHUNK_CONTENT] Event #347 - Content preview: 'NO_CONTENT...'
2025-06-23 16:04:46,279 - athlea_langgraph.api.main - INFO - 🔍 ONBOARDING: [STREAM_TOKEN] Event #348 - Event: AzureChatOpenAI, Tags: ['seq:step:1', 'internal_processing', 'transition_message'], Metadata: {'thread_id': '35cb3a66-e1db-49fa-bb48-a8441798283b', 'langgraph_step': 12, 'langgraph_node': 'gatherInfo', 'langgraph_triggers': ('branch:to:gatherInfo',), 'langgraph_path': ('__pregel_pull', 'gatherInfo'), 'langgraph_checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'checkpoint_ns': 'gatherInfo:363c7ac8-3be8-2cb8-13e7-4a593537c8de', 'ls_provider': 'azure', 'ls_model_name': 'gpt-4.1-nano', 
2025-06-23 16:04:47,141 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Transition Message] Created dynamic message with 3 info points
2025-06-23 16:04:47,142 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Created dynamic transition message: 812 chars
2025-06-23 16:04:47,142 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] DEBUG: current_onboarding_stage='gathering', current_sidebar_stage='plan_summary_ready'
2025-06-23 16:04:47,142 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] 🚫 PRESERVING plan_summary_ready stage to prevent loop
2025-06-23 16:04:47,142 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Goals: ['Improve at Running', 'Improve at Cycling', 'Improve at Strength Training', 'lose 20 pounds', 'improve cardiovascular endurance', 'build functional strength for daily activities', 'run a 10K race in 6 months', 'Lose 20 pounds', 'Improve cardiovascular endurance', 'Build functional strength for daily activities', 'Run a 10K race in 6 months'], SummaryItems: 9, Selected Sport: Strength Training
2025-06-23 16:04:47,142 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Setting has_enough_info: True (should_transition_to_plan: True, is_first_turn: False)
2025-06-23 16:04:47,142 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] 🚫 NOT resetting plan_summary_sent (preserving existing state)
2025-06-23 16:04:47,142 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] Saving onboarding sidebar data for user: oocR78vBmCeHSuMu7ORq1Wo6df23, thread_id: None
2025-06-23 16:04:47,184 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [MongoDB] Successfully saved onboarding data for user: oocR78vBmCeHSuMu7ORq1Wo6df23
2025-06-23 16:04:47,184 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Profile Update] Persisting onboarding data to training profile for user: oocR78vBmCeHSuMu7ORq1Wo6df23
2025-06-23 16:04:47,184 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Profile Update] Adding goals: Improve at Running, Improve at Cycling, Improve at Strength Training, lose 20 pounds, improve cardiovascular endurance, build functional strength for daily activities, run a 10K race in 6 months, Lose 20 pounds, Improve cardiovascular endurance, Build functional strength for daily activities, Run a 10K race in 6 months
2025-06-23 16:04:47,184 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Profile Update] Adding sports: Running, Cycling, Strength Training
2025-06-23 16:04:47,184 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Profile Update] Calling update-profile API at http://localhost:3000/api/update-profile
2025-06-23 16:04:48,885 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Profile Update] Successfully updated training profile for user oocR78vBmCeHSuMu7ORq1Wo6df23
2025-06-23 16:04:48,885 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Profile Update] Updated fields: {'general': {'weight': 115}, 'nutrition': {'status': 'new info'}}
2025-06-23 16:04:48,886 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] *** FINAL UPDATES BEING RETURNED ***
2025-06-23 16:04:48,886 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.sport_suggestions: None
2025-06-23 16:04:48,886 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.selected_sports: ['Running', 'Cycling', 'Strength Training']
2025-06-23 16:04:48,886 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.selected_sport: Strength Training
2025-06-23 16:04:48,886 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.goals.list: ['Improve at Running', 'Improve at Cycling', 'Improve at Strength Training', 'lose 20 pounds', 'improve cardiovascular endurance', 'build functional strength for daily activities', 'run a 10K race in 6 months', 'Lose 20 pounds', 'Improve cardiovascular endurance', 'Build functional strength for daily activities', 'Run a 10K race in 6 months']
2025-06-23 16:04:48,886 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.goals.exists: True
2025-06-23 16:04:48,886 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.summary_items count: 9
2025-06-23 16:04:48,886 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.summary_items: ['Fitness Goals: Lose 20 pounds, improve cardiovascular endurance, build functional strength for daily activities', 'Experience Level: Beginner runner, some experience with basic strength training, current fitness level intermediate', 'Time Availability: 4-5 workouts per week, mornings around 6 AM before work', 'Equipment Access: Full gym equipment, resistance bands at home, running shoes, basic workout clothes', 'Physical Considerations: Minor knee issues from playing soccer, need to avoid high-impact activities', 'Preferences: Lower intensity steady workouts preferred over high-intensity intervals', 'Nutrition: Decent diet but high processed food intake, low protein, willing to meal prep and track food', 'Goals/Timeline: Noticeable improvements in 3 months, run a 10K in 6 months, lose 20 pounds by then', 'Goals: Lose 20 pounds, improve cardiovascular endurance, build functional strength for daily activities, run a 10K in 6 months']
2025-06-23 16:04:48,886 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.key_insights count: 0
2025-06-23 16:04:48,886 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.key_insights: {}
2025-06-23 16:04:48,886 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [DEBUG] sidebar_data.current_stage: plan_summary_ready
2025-06-23 16:04:48,886 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] 🎯 RETURNING UPDATES - NODE EXECUTION COMPLETE
2025-06-23 16:04:48,886 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Updates keys: ['messages', 'sidebar_data', 'has_enough_info']
2025-06-23 16:04:48,886 - athlea_langgraph.agents.onboarding.information_gatherer_node - INFO - [Node: informationGathererNode] Messages count: 1
2025-06-23 16:04:48,886 - athlea_langgraph.graphs.onboarding_graph - INFO - 🧠 [ONBOARDING] information_gatherer_node completed! Result keys: ['messages', 'sidebar_data', 'has_enough_info']
2025-06-23 16:04:48,887 - athlea_langgraph.graphs.onboarding_graph - INFO - [Graph Router] _after_gather_info_router - plan_summary_sent: False, onboarding_stage: plan_summary_ready
2025-06-23 16:04:48,888 - athlea_langgraph.graphs.onboarding_graph - INFO - [Graph Router] 🚫 LOOP PREVENTION: User at plan summary stage - ending to prevent infinite loop
2025-06-23 16:04:48,888 - athlea_langgraph.graphs.onboarding_graph - INFO - [Graph Router] Plan summary confirmation should be handled by _plan_summary_confirmation_router
2025-06-23 16:04:48,890 - athlea_langgraph.api.main - INFO - 📈 ONBOARDING: [STREAM] Processed 520 events so far. Event types seen: ['on_chain_end', 'on_chain_start', 'on_chain_stream', 'on_chat_model_end', 'on_chat_model_start', 'on_chat_model_stream', 'on_parser_end', 'on_parser_start']
2025-06-23 16:04:48,891 - athlea_langgraph.api.main - INFO - 📋 ONBOARDING: [SIDEBAR_UPDATE] Sending sidebar data update (data changed)
2025-06-23 16:04:48,891 - athlea_langgraph.api.main - INFO - 📋 ONBOARDING: [SIDEBAR_DATA] Goals: {'exists': True, 'list': ['Improve at Running', 'Improve at Cycling', 'Improve at Strength Training', 'lose 20 pounds', 'improve cardiovascular endurance', 'build functional strength for daily activities', 'run a 10K race in 6 months', 'Lose 20 pounds', 'Improve cardiovascular endurance', 'Build functional strength for daily activities', 'Run a 10K race in 6 months']}
2025-06-23 16:04:48,891 - athlea_langgraph.api.main - INFO - 📋 ONBOARDING: [SIDEBAR_DATA] Selected Sports: ['Running', 'Cycling', 'Strength Training']
2025-06-23 16:04:48,891 - athlea_langgraph.api.main - INFO - 📋 ONBOARDING: [SIDEBAR_DATA] Summary Items Count: 9
2025-06-23 16:04:48,892 - athlea_langgraph.api.main - INFO - 📤 ONBOARDING: [SSE_SEND] Completion event sent
2025-06-23 16:04:48,892 - athlea_langgraph.api.main - INFO - 🏁 ONBOARDING: [STREAM_END] Completed streaming, total events processed: 520
2025-06-23 16:04:48,892 - athlea_langgraph.api.main - INFO - 📊 ONBOARDING: [STREAM_SUMMARY] Agent starts sent: ['Athlea']
2025-06-23 16:04:48,892 - athlea_langgraph.api.main - WARNING - ⚠️ ONBOARDING: [TOKEN_SUMMARY] No tokens were produced by any nodes!
2025-06-23 16:04:48,892 - athlea_langgraph.api.main - INFO - ⏱️ ONBOARDING: [STREAM_COMPLETE] Total streaming time: 6.90s